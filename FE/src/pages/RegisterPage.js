import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, UserPlus, User, Briefcase } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import AuthLayout from '../components/AuthLayout';

const RegisterPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [userType, setUserType] = useState('Customer');
  const { register: registerUser, loading } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    watch
  } = useForm({
    defaultValues: {
      userType: 'Customer'
    }
  });

  const watchedPassword = watch('password');
  const watchedUserType = watch('userType');

  const onSubmit = async (data) => {
    const result = await registerUser(data);
    if (result.success) {
      navigate('/dashboard');
    } else {
      setError('root', { message: result.error });
    }
  };

  return (
    <AuthLayout 
      title="Create your account"
      subtitle="Join Cinema today! Please fill in your details."
    >
      <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
        {/* User Type Selection */}
        <div className="form-group">
          <label className="form-label">Account Type</label>
          <div className="grid grid-cols-2 gap-3">
            <label className={`relative flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              watchedUserType === 'Customer' 
                ? 'border-primary-500 bg-primary-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}>
              <input
                type="radio"
                value="Customer"
                className="sr-only"
                {...register('userType', { required: 'Please select account type' })}
                onChange={(e) => setUserType(e.target.value)}
              />
              <User className="h-5 w-5 text-primary-600 mr-2" />
              <span className="text-sm font-medium">Customer</span>
            </label>
            
            <label className={`relative flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
              watchedUserType === 'Staff' 
                ? 'border-primary-500 bg-primary-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}>
              <input
                type="radio"
                value="Staff"
                className="sr-only"
                {...register('userType', { required: 'Please select account type' })}
                onChange={(e) => setUserType(e.target.value)}
              />
              <Briefcase className="h-5 w-5 text-primary-600 mr-2" />
              <span className="text-sm font-medium">Staff</span>
            </label>
          </div>
          {errors.userType && (
            <p className="form-error">{errors.userType.message}</p>
          )}
        </div>

        {/* Username */}
        <div className="form-group">
          <label htmlFor="username" className="form-label">
            Username
          </label>
          <input
            id="username"
            type="text"
            autoComplete="username"
            className={`input ${errors.username ? 'border-red-500' : ''}`}
            placeholder="Choose a username"
            {...register('username', {
              required: 'Username is required',
              minLength: {
                value: 3,
                message: 'Username must be at least 3 characters'
              },
              pattern: {
                value: /^[a-zA-Z0-9]+$/,
                message: 'Username can only contain letters and numbers'
              }
            })}
          />
          {errors.username && (
            <p className="form-error">{errors.username.message}</p>
          )}
        </div>

        {/* Personal Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="form-group">
            <label htmlFor="firstName" className="form-label">
              First Name
            </label>
            <input
              id="firstName"
              type="text"
              autoComplete="given-name"
              className={`input ${errors.firstName ? 'border-red-500' : ''}`}
              placeholder="First name"
              {...register('firstName', {
                required: 'First name is required',
                minLength: {
                  value: 1,
                  message: 'First name is required'
                }
              })}
            />
            {errors.firstName && (
              <p className="form-error">{errors.firstName.message}</p>
            )}
          </div>

        {/* Address Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="form-group">
            <label htmlFor="city" className="form-label">
              City
            </label>
            <input
              id="city"
              type="text"
              autoComplete="address-level2"
              className={`input ${errors.city ? 'border-red-500' : ''}`}
              placeholder="City"
              {...register('city', {
                required: 'City is required',
                minLength: {
                  value: 1,
                  message: 'City is required'
                }
              })}
            />
            {errors.city && (
              <p className="form-error">{errors.city.message}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="street" className="form-label">
              Street Address
            </label>
            <input
              id="street"
              type="text"
              autoComplete="street-address"
              className={`input ${errors.street ? 'border-red-500' : ''}`}
              placeholder="Street address"
              {...register('street', {
                required: 'Street address is required',
                minLength: {
                  value: 1,
                  message: 'Street address is required'
                }
              })}
            />
            {errors.street && (
              <p className="form-error">{errors.street.message}</p>
            )}
          </div>
        </div>

        {/* Staff-specific fields */}
        {watchedUserType === 'Staff' && (
          <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-900">Staff Information</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                <label htmlFor="salary" className="form-label">
                  Salary
                </label>
                <input
                  id="salary"
                  type="number"
                  min="0"
                  step="1000"
                  className={`input ${errors.salary ? 'border-red-500' : ''}`}
                  placeholder="Monthly salary"
                  {...register('salary', {
                    required: watchedUserType === 'Staff' ? 'Salary is required for staff' : false,
                    min: {
                      value: 0,
                      message: 'Salary must be positive'
                    }
                  })}
                />
                {errors.salary && (
                  <p className="form-error">{errors.salary.message}</p>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="position" className="form-label">
                  Position
                </label>
                <input
                  id="position"
                  type="text"
                  className={`input ${errors.position ? 'border-red-500' : ''}`}
                  placeholder="Job position"
                  {...register('position', {
                    required: watchedUserType === 'Staff' ? 'Position is required for staff' : false,
                    minLength: {
                      value: 1,
                      message: 'Position is required'
                    }
                  })}
                />
                {errors.position && (
                  <p className="form-error">{errors.position.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                <label htmlFor="managerCode" className="form-label">
                  Manager Code (Optional)
                </label>
                <input
                  id="managerCode"
                  type="text"
                  className="input"
                  placeholder="Manager code"
                  {...register('managerCode')}
                />
              </div>

              <div className="form-group">
                <label htmlFor="title" className="form-label">
                  Title (Optional)
                </label>
                <input
                  id="title"
                  type="text"
                  className="input"
                  placeholder="Job title"
                  {...register('title')}
                />
              </div>
            </div>
          </div>
        )}

        {/* Password */}
        <div className="form-group">
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              className={`input pr-10 ${errors.password ? 'border-red-500' : ''}`}
              placeholder="Create a password"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 6,
                  message: 'Password must be at least 6 characters'
                }
              })}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="form-error">{errors.password.message}</p>
          )}
        </div>

        {/* Confirm Password */}
        <div className="form-group">
          <label htmlFor="confirmPassword" className="form-label">
            Confirm Password
          </label>
          <div className="relative">
            <input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              autoComplete="new-password"
              className={`input pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}
              placeholder="Confirm your password"
              {...register('confirmPassword', {
                required: 'Please confirm your password',
                validate: value =>
                  value === watchedPassword || 'Passwords do not match'
              })}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="form-error">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Global error */}
        {errors.root && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{errors.root.message}</p>
          </div>
        )}

        {/* Submit button */}
        <button
          type="submit"
          disabled={loading}
          className="btn-primary w-full flex items-center justify-center space-x-2"
        >
          {loading ? (
            <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          ) : (
            <>
              <UserPlus className="h-4 w-4" />
              <span>Create Account</span>
            </>
          )}
        </button>

        {/* Login link */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              to="/login"
              className="font-medium text-primary-600 hover:text-primary-500 transition-colors"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
};

export default RegisterPage;

          <div className="form-group">
            <label htmlFor="lastName" className="form-label">
              Last Name
            </label>
            <input
              id="lastName"
              type="text"
              autoComplete="family-name"
              className={`input ${errors.lastName ? 'border-red-500' : ''}`}
              placeholder="Last name"
              {...register('lastName', {
                required: 'Last name is required',
                minLength: {
                  value: 1,
                  message: 'Last name is required'
                }
              })}
            />
            {errors.lastName && (
              <p className="form-error">{errors.lastName.message}</p>
            )}
          </div>
        </div>
