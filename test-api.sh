#!/bin/bash

# Cinema Project API Testing Script
echo "🧪 Testing Cinema Project APIs..."

BASE_URL="http://localhost:3001/api/auth"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $status_code)"
        echo "Response: $body" | jq '.' 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response: $body"
    fi
}

# Wait for service to be ready
echo "⏳ Waiting for auth service to be ready..."
until curl -s http://localhost:3001/api/auth/health > /dev/null; do
    echo "Waiting for auth service..."
    sleep 2
done

echo -e "${GREEN}🚀 Auth service is ready!${NC}"

# Test 1: Health Check
test_endpoint "GET" "/health" "" 200 "Health Check"

# Test 2: Register Customer
CUSTOMER_DATA='{
    "username": "testcustomer",
    "password": "password123",
    "confirmPassword": "password123",
    "userType": "Customer",
    "firstName": "Test",
    "lastName": "Customer",
    "city": "Ho Chi Minh City",
    "street": "123 Test Street"
}'

test_endpoint "POST" "/register" "$CUSTOMER_DATA" 201 "Register Customer"

# Test 3: Register Staff
STAFF_DATA='{
    "username": "teststaff",
    "password": "password123",
    "confirmPassword": "password123",
    "userType": "Staff",
    "firstName": "Test",
    "lastName": "Staff",
    "city": "Hanoi",
    "street": "456 Staff Avenue",
    "salary": 20000000,
    "position": "Manager",
    "title": "Senior Manager"
}'

test_endpoint "POST" "/register" "$STAFF_DATA" 201 "Register Staff"

# Test 4: Login with Customer
LOGIN_DATA='{
    "username": "testcustomer",
    "password": "password123"
}'

echo -e "\n${YELLOW}Testing: Customer Login${NC}"
login_response=$(curl -s -X POST "$BASE_URL/login" \
    -H "Content-Type: application/json" \
    -d "$LOGIN_DATA")

echo "Response: $login_response" | jq '.' 2>/dev/null || echo "Response: $login_response"

# Extract token for protected route testing
ACCESS_TOKEN=$(echo "$login_response" | jq -r '.data.accessToken' 2>/dev/null)

if [ "$ACCESS_TOKEN" != "null" ] && [ -n "$ACCESS_TOKEN" ]; then
    echo -e "${GREEN}✅ Login successful, token extracted${NC}"
    
    # Test 5: Get Profile (Protected Route)
    echo -e "\n${YELLOW}Testing: Get Profile (Protected)${NC}"
    profile_response=$(curl -s -X GET "$BASE_URL/profile" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "Response: $profile_response" | jq '.' 2>/dev/null || echo "Response: $profile_response"
    
    # Test 6: Logout
    test_endpoint "POST" "/logout" "" 200 "Logout"
else
    echo -e "${RED}❌ Could not extract access token from login response${NC}"
fi

# Test 7: Invalid Login
INVALID_LOGIN='{
    "username": "nonexistent",
    "password": "wrongpassword"
}'

test_endpoint "POST" "/login" "$INVALID_LOGIN" 401 "Invalid Login"

# Test 8: Duplicate Registration
test_endpoint "POST" "/register" "$CUSTOMER_DATA" 400 "Duplicate Registration"

# Test 9: Invalid Registration (missing fields)
INVALID_REGISTER='{
    "username": "incomplete"
}'

test_endpoint "POST" "/register" "$INVALID_REGISTER" 400 "Invalid Registration"

# Test 10: Login with seeded accounts
echo -e "\n${YELLOW}Testing seeded accounts...${NC}"

SEEDED_CUSTOMER='{
    "username": "customer1",
    "password": "password123"
}'

test_endpoint "POST" "/login" "$SEEDED_CUSTOMER" 200 "Seeded Customer Login"

SEEDED_STAFF='{
    "username": "staff1",
    "password": "password123"
}'

test_endpoint "POST" "/login" "$SEEDED_STAFF" 200 "Seeded Staff Login"

SEEDED_ADMIN='{
    "username": "admin",
    "password": "admin123"
}'

test_endpoint "POST" "/login" "$SEEDED_ADMIN" 200 "Seeded Admin Login"

echo -e "\n${GREEN}🎉 API Testing Complete!${NC}"
echo -e "\n📊 Summary:"
echo "- Health check endpoint working"
echo "- User registration working (Customer & Staff)"
echo "- User login working"
echo "- Protected routes working"
echo "- Input validation working"
echo "- Seeded test accounts working"
