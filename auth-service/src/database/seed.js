const bcrypt = require('bcryptjs');
const { User, Address, FullName, Customer, Staff } = require('../models');

const seed = async () => {
  try {
    console.log('Starting database seeding...');
    
    // Create sample addresses
    const address1 = await Address.create({
      City: 'Ho Chi Minh City',
      Street: '123 Nguyen Hue Street'
    });
    
    const address2 = await Address.create({
      City: 'Hanoi',
      Street: '456 Hoan Kiem Street'
    });
    
    // Create sample full names
    const fullName1 = await FullName.create({
      FirstName: 'John',
      LastName: 'Doe'
    });
    
    const fullName2 = await FullName.create({
      FirstName: 'Jane',
      LastName: 'Smith'
    });
    
    const fullName3 = await FullName.create({
      FirstName: 'Admin',
      LastName: 'User'
    });
    
    // Hash passwords
    const hashedPassword = await bcrypt.hash('password123', 12);
    const hashedAdminPassword = await bcrypt.hash('admin123', 12);
    
    // Create sample users
    const customerUser = await User.create({
      Discriminator: 'Customer',
      username: 'customer1',
      password: hashedPassword,
      AddressId: address1.Id,
      FullNameId: fullName1.Id
    });
    
    const staffUser = await User.create({
      Discriminator: 'Staff',
      username: 'staff1',
      password: hashedPassword,
      AddressId: address2.Id,
      FullNameId: fullName2.Id
    });
    
    const adminUser = await User.create({
      Discriminator: 'Staff',
      username: 'admin',
      password: hashedAdminPassword,
      AddressId: address2.Id,
      FullNameId: fullName3.Id
    });
    
    // Create customer profile
    await Customer.create({
      UserId: customerUser.Id,
      RewardPoint: 100,
      CustomerRanking: 'Silver'
    });
    
    // Create staff profiles
    await Staff.create({
      UserId: staffUser.Id,
      Salary: 15000000,
      Position: 'Cashier',
      ManagerCode: 'MGR001',
      Title: 'Senior Cashier'
    });
    
    await Staff.create({
      UserId: adminUser.Id,
      Salary: 25000000,
      Position: 'Manager',
      ManagerCode: null,
      Title: 'Cinema Manager'
    });
    

    console.log('   Customer: username=customer1, password=password123');
    console.log('   Staff: username=staff1, password=password123');
    console.log('   Admin: username=admin, password=admin123');
    
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seed().then(() => {
    process.exit(0);
  });
}

module.exports = seed;
