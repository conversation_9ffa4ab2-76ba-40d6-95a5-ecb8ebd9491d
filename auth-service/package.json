{"name": "auth-service", "version": "1.0.0", "description": "Node.js Authentication Service for Cinema Project", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "db:setup": "npm run migrate && npm run seed"}, "keywords": ["nodejs", "express", "jwt", "authentication", "cinema"], "author": "Cinema Project Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "sequelize": "^6.35.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.10", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}